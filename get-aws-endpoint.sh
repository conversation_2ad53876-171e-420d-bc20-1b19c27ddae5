#!/bin/bash

# Script to get the public endpoint of your deployed Flask AI Wrapper on AWS
set -e

# Configuration
AWS_REGION="us-east-1"
CLUSTER_NAME="flask-ai-cluster"
SERVICE_NAME="flask-ai-service"

echo "Getting Flask AI Wrapper endpoint information..."

# Get the task ARN
TASK_ARN=$(aws ecs list-tasks \
    --cluster $CLUSTER_NAME \
    --service-name $SERVICE_NAME \
    --query 'taskArns[0]' \
    --output text \
    --region $AWS_REGION)

if [ "$TASK_ARN" = "None" ] || [ -z "$TASK_ARN" ]; then
    echo "No running tasks found for service $SERVICE_NAME"
    echo "Check if the service is running:"
    echo "aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $AWS_REGION"
    exit 1
fi

echo "Found task: $TASK_ARN"

# Get the network interface ID
NETWORK_INTERFACE_ID=$(aws ecs describe-tasks \
    --cluster $CLUSTER_NAME \
    --tasks $TASK_ARN \
    --query 'tasks[0].attachments[0].details[?name==`networkInterfaceId`].value' \
    --output text \
    --region $AWS_REGION)

if [ -z "$NETWORK_INTERFACE_ID" ]; then
    echo "Could not find network interface ID"
    exit 1
fi

echo "Network Interface ID: $NETWORK_INTERFACE_ID"

# Get the public IP
PUBLIC_IP=$(aws ec2 describe-network-interfaces \
    --network-interface-ids $NETWORK_INTERFACE_ID \
    --query 'NetworkInterfaces[0].Association.PublicIp' \
    --output text \
    --region $AWS_REGION)

if [ "$PUBLIC_IP" = "None" ] || [ -z "$PUBLIC_IP" ]; then
    echo "No public IP found. The task might not have a public IP assigned."
    exit 1
fi

echo ""
echo "🎉 Your Flask AI Wrapper is accessible at:"
echo "http://$PUBLIC_IP:5000"
echo ""
echo "Test endpoints:"
echo "Health check: curl http://$PUBLIC_IP:5000/"
echo "Chat: curl -X POST http://$PUBLIC_IP:5000/chat -H 'Content-Type: application/json' -d '{\"message\":\"Hello\"}'"
echo "Route assistance: curl -X POST http://$PUBLIC_IP:5000/route-assistance -H 'Content-Type: application/json' -d '{\"message\":\"Best route to downtown?\"}'"
echo ""
echo "For your Laravel app, use this base URL:"
echo "http://$PUBLIC_IP:5000"
