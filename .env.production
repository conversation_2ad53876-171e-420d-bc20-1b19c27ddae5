# Production environment variables for Azure deployment
FLASK_ENV=production
FLASK_DEBUG=False
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# AI Model Configuration - Lightweight models that run without external dependencies
AI_MODEL=microsoft/DialoGPT-small
MAX_LENGTH=512

# Alternative models (uncomment to use):
# AI_MODEL=distilgpt2
# AI_MODEL=TinyLlama/TinyLlama-1.1B-Chat-v1.0

# Security - Generate a strong secret key
SECRET_KEY=your-production-secret-key-here

# CORS Settings - Update with your Laravel app domain
CORS_ORIGINS=https://your-laravel-app.com,https://www.your-laravel-app.com

# Logging
LOG_LEVEL=INFO

# Azure specific settings
AZURE_STORAGE_CONNECTION_STRING=your-azure-storage-connection-string
AZURE_APPLICATION_INSIGHTS_KEY=your-app-insights-key
