# Azure Container Instances deployment with Ollama
apiVersion: 2019-12-01
location: eastus
name: ollama-flask-group
properties:
  containers:
  # Ollama container
  - name: ollama
    properties:
      image: ollama/ollama:latest
      resources:
        requests:
          cpu: 2.0
          memoryInGb: 4.0
      ports:
      - port: 11434
        protocol: TCP
      volumeMounts:
      - name: ollama-data
        mountPath: /root/.ollama
      
  # Flask app container
  - name: flask-ollama-wrapper
    properties:
      image: your-registry/flask-ollama-wrapper:latest
      resources:
        requests:
          cpu: 1.0
          memoryInGb: 2.0
      ports:
      - port: 5000
        protocol: TCP
      environmentVariables:
      - name: FLASK_ENV
        value: production
      - name: FLASK_DEBUG
        value: "False"
      - name: OLLAMA_HOST
        value: "http://localhost:11434"
      - name: OLLAMA_MODEL
        value: "llama2"
      - name: SECRET_KEY
        secureValue: "your-secret-key"
      - name: CORS_ORIGINS
        value: "https://your-laravel-app.com"
      - name: LOG_LEVEL
        value: "INFO"
        
  volumes:
  - name: ollama-data
    azureFile:
      shareName: ollama-data
      storageAccountName: your-storage-account
      storageAccountKey: your-storage-key
      
  osType: Linux
  restartPolicy: Always
  ipAddress:
    type: Public
    ports:
    - protocol: TCP
      port: 5000
    - protocol: TCP
      port: 11434
    dnsNameLabel: ollama-flask-app
    
tags:
  environment: production
  service: ollama-flask-wrapper
