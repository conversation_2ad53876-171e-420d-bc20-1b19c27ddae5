@echo off
echo Fixing npm accessibility on Windows...
echo.

REM Check if Node.js is installed
echo Checking Node.js installation...
node --version
if errorlevel 1 (
    echo Node.js is not accessible. Please reinstall Node.js.
    pause
    exit /b 1
)

echo Node.js version found: 
node --version
echo.

REM Check npm accessibility
echo Checking npm accessibility...
npm --version 2>nul
if errorlevel 1 (
    echo npm is not accessible. Attempting to fix...
    
    REM Get Node.js installation path
    for /f "tokens=*" %%i in ('where node 2^>nul') do set NODE_PATH=%%i
    
    if defined NODE_PATH (
        echo Node.js found at: %NODE_PATH%
        
        REM Get the directory containing node.exe
        for %%F in ("%NODE_PATH%") do set NODE_DIR=%%~dpF
        echo Node.js directory: %NODE_DIR%
        
        REM Check if npm exists in the same directory
        if exist "%NODE_DIR%npm.cmd" (
            echo npm.cmd found at: %NODE_DIR%npm.cmd
            
            REM Add Node.js directory to PATH if not already there
            echo %PATH% | findstr /i "%NODE_DIR%" >nul
            if errorlevel 1 (
                echo Adding Node.js directory to PATH...
                setx PATH "%PATH%;%NODE_DIR%" /M
                echo Please restart your command prompt and try again.
            ) else (
                echo Node.js directory is already in PATH.
            )
        ) else (
            echo npm.cmd not found in Node.js directory.
            echo This might be a corrupted installation.
        )
    ) else (
        echo Could not locate Node.js installation path.
    )
) else (
    echo npm is working correctly!
    npm --version
)

echo.
echo Troubleshooting complete.
pause
