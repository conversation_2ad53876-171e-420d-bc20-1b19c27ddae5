# Azure Container Instances deployment configuration for lightweight AI wrapper
apiVersion: 2019-12-01
location: eastus
name: flask-ai-wrapper
properties:
  containers:
  - name: flask-ai-wrapper
    properties:
      image: your-registry/flask-ai-wrapper:latest
      resources:
        requests:
          cpu: 2.0
          memoryInGb: 4.0
      ports:
      - port: 5000
        protocol: TCP
      environmentVariables:
      - name: FLASK_ENV
        value: production
      - name: FLASK_DEBUG
        value: "False"
      - name: FLASK_HOST
        value: "0.0.0.0"
      - name: FLASK_PORT
        value: "5000"
      - name: AI_MODEL
        value: "microsoft/DialoGPT-small"
      - name: MAX_LENGTH
        value: "512"
      - name: SECRET_KEY
        secureValue: "your-secret-key"
      - name: CORS_ORIGINS
        value: "https://your-laravel-app.com"
      - name: LOG_LEVEL
        value: "INFO"
  osType: Linux
  restartPolicy: Always
  ipAddress:
    type: Public
    ports:
    - protocol: TCP
      port: 5000
    dnsNameLabel: flask-ai-wrapper
tags:
  environment: production
  service: flask-ai-wrapper
