#!/bin/bash

# AWS Deployment Script for Flask AI Wrapper
set -e

# Configuration - Update these values
AWS_REGION="us-east-1"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
REPOSITORY_NAME="flask-ai-wrapper"
CLUSTER_NAME="flask-ai-cluster"
SERVICE_NAME="flask-ai-service"
TASK_FAMILY="flask-ai-wrapper"

echo "Starting AWS deployment for Flask AI Wrapper..."
echo "AWS Account ID: $AWS_ACCOUNT_ID"
echo "Region: $AWS_REGION"

# Step 1: Create ECR repository if it doesn't exist
echo "Creating ECR repository..."
aws ecr describe-repositories --repository-names $REPOSITORY_NAME --region $AWS_REGION 2>/dev/null || \
aws ecr create-repository --repository-name $REPOSITORY_NAME --region $AWS_REGION

# Step 2: Get ECR login
echo "Logging into ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# Step 3: Build Docker image
echo "Building Docker image..."
docker build -t $REPOSITORY_NAME .

# Step 4: Tag and push image
echo "Tagging and pushing image to ECR..."
docker tag $REPOSITORY_NAME:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$REPOSITORY_NAME:latest
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$REPOSITORY_NAME:latest

# Step 5: Create ECS cluster if it doesn't exist
echo "Creating ECS cluster..."
aws ecs describe-clusters --clusters $CLUSTER_NAME --region $AWS_REGION 2>/dev/null || \
aws ecs create-cluster --cluster-name $CLUSTER_NAME --capacity-providers FARGATE --default-capacity-provider-strategy capacityProvider=FARGATE,weight=1 --region $AWS_REGION

# Step 6: Create IAM role if it doesn't exist
echo "Creating IAM role..."
aws iam get-role --role-name flask-ai-wrapper-task-role 2>/dev/null || {
    cat > ecs-task-trust-policy.json << 'EOF'
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ecs-tasks.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

    aws iam create-role --role-name flask-ai-wrapper-task-role --assume-role-policy-document file://ecs-task-trust-policy.json
    aws iam attach-role-policy --role-name flask-ai-wrapper-task-role --policy-arn arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
    rm ecs-task-trust-policy.json
}

# Step 7: Create CloudWatch log group
echo "Creating CloudWatch log group..."
aws logs describe-log-groups --log-group-name-prefix "/ecs/$TASK_FAMILY" --region $AWS_REGION | grep -q "$TASK_FAMILY" || \
aws logs create-log-group --log-group-name "/ecs/$TASK_FAMILY" --region $AWS_REGION

# Step 8: Register task definition
echo "Registering task definition..."
cat > task-definition.json << EOF
{
  "family": "$TASK_FAMILY",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "3072",
  "executionRoleArn": "arn:aws:iam::$AWS_ACCOUNT_ID:role/flask-ai-wrapper-task-role",
  "containerDefinitions": [
    {
      "name": "$REPOSITORY_NAME",
      "image": "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$REPOSITORY_NAME:latest",
      "portMappings": [
        {
          "containerPort": 5000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {"name": "FLASK_ENV", "value": "production"},
        {"name": "FLASK_DEBUG", "value": "False"},
        {"name": "FLASK_HOST", "value": "0.0.0.0"},
        {"name": "FLASK_PORT", "value": "5000"},
        {"name": "AI_MODEL", "value": "microsoft/DialoGPT-small"},
        {"name": "MAX_LENGTH", "value": "512"},
        {"name": "SECRET_KEY", "value": "$(openssl rand -base64 32)"},
        {"name": "CORS_ORIGINS", "value": "*"},
        {"name": "LOG_LEVEL", "value": "INFO"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/$TASK_FAMILY",
          "awslogs-region": "$AWS_REGION",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:5000/ || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
EOF

aws ecs register-task-definition --cli-input-json file://task-definition.json --region $AWS_REGION
rm task-definition.json

# Step 9: Get default VPC and subnets
echo "Getting VPC information..."
VPC_ID=$(aws ec2 describe-vpcs --filters "Name=is-default,Values=true" --query 'Vpcs[0].VpcId' --output text --region $AWS_REGION)
SUBNET_IDS=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" --query 'Subnets[0:2].SubnetId' --output text --region $AWS_REGION)
SUBNET_ARRAY=($SUBNET_IDS)

echo "Using VPC: $VPC_ID"
echo "Using Subnets: ${SUBNET_ARRAY[@]}"

# Step 10: Create security group
echo "Creating security group..."
SG_ID=$(aws ec2 create-security-group \
    --group-name flask-ai-sg-$(date +%s) \
    --description "Security group for Flask AI wrapper" \
    --vpc-id $VPC_ID \
    --query 'GroupId' \
    --output text \
    --region $AWS_REGION)

# Allow HTTP traffic
aws ec2 authorize-security-group-ingress \
    --group-id $SG_ID \
    --protocol tcp \
    --port 80 \
    --cidr 0.0.0.0/0 \
    --region $AWS_REGION

# Allow container port
aws ec2 authorize-security-group-ingress \
    --group-id $SG_ID \
    --protocol tcp \
    --port 5000 \
    --cidr 0.0.0.0/0 \
    --region $AWS_REGION

echo "Created Security Group: $SG_ID"

# Step 11: Create or update ECS service
echo "Creating/updating ECS service..."
aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $AWS_REGION 2>/dev/null | grep -q "ACTIVE" && {
    echo "Updating existing service..."
    aws ecs update-service \
        --cluster $CLUSTER_NAME \
        --service $SERVICE_NAME \
        --task-definition $TASK_FAMILY \
        --region $AWS_REGION
} || {
    echo "Creating new service..."
    aws ecs create-service \
        --cluster $CLUSTER_NAME \
        --service-name $SERVICE_NAME \
        --task-definition $TASK_FAMILY \
        --desired-count 1 \
        --launch-type FARGATE \
        --network-configuration "awsvpcConfiguration={subnets=[${SUBNET_ARRAY[0]},${SUBNET_ARRAY[1]}],securityGroups=[$SG_ID],assignPublicIp=ENABLED}" \
        --region $AWS_REGION
}

echo ""
echo "Deployment completed! 🎉"
echo ""
echo "Your Flask AI Wrapper is being deployed to AWS ECS."
echo "It may take a few minutes for the service to become available."
echo ""
echo "To check the status:"
echo "aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $AWS_REGION"
echo ""
echo "To get the public IP:"
echo "aws ecs describe-tasks --cluster $CLUSTER_NAME --tasks \$(aws ecs list-tasks --cluster $CLUSTER_NAME --service-name $SERVICE_NAME --query 'taskArns[0]' --output text --region $AWS_REGION) --query 'tasks[0].attachments[0].details[?name==\`networkInterfaceId\`].value' --output text --region $AWS_REGION | xargs -I {} aws ec2 describe-network-interfaces --network-interface-ids {} --query 'NetworkInterfaces[0].Association.PublicIp' --output text --region $AWS_REGION"
