# AWS Deployment Guide - Flask AI Wrapper

## Prerequisites

1. **AWS Account** with billing enabled
2. **AWS CLI** installed and configured
3. **Docker** installed locally
4. **Git** for version control

## Step 1: Install and Configure AWS CLI

### Install AWS CLI
```bash
# Windows (using pip)
pip install awscli

# macOS
brew install awscli

# Linux
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

### Configure AWS CLI
```bash
aws configure
# Enter your:
# AWS Access Key ID
# AWS Secret Access Key  
# Default region (e.g., us-east-1)
# Default output format (json)
```

## Step 2: Create AWS Resources

### Create IAM Role for ECS
```bash
# Create trust policy file
cat > ecs-task-trust-policy.json << 'EOF'
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ecs-tasks.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

# Create IAM role
aws iam create-role \
    --role-name flask-ai-wrapper-task-role \
    --assume-role-policy-document file://ecs-task-trust-policy.json

# Attach policies
aws iam attach-role-policy \
    --role-name flask-ai-wrapper-task-role \
    --policy-arn arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
```

### Create ECR Repository
```bash
# Create repository for your Docker image
aws ecr create-repository \
    --repository-name flask-ai-wrapper \
    --region us-east-1

# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <your-account-id>.dkr.ecr.us-east-1.amazonaws.com
```

## Step 3: Prepare Your Application

### Update Environment Configuration
Create `.env.aws`:
```bash
FLASK_ENV=production
FLASK_DEBUG=False
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
AI_MODEL=microsoft/DialoGPT-small
MAX_LENGTH=512
SECRET_KEY=your-super-secret-key-change-this
CORS_ORIGINS=https://your-laravel-app.com
LOG_LEVEL=INFO
```

### Create AWS-Optimized Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Pre-download the AI model to reduce startup time
RUN python -c "from transformers import AutoTokenizer, AutoModelForCausalLM; AutoTokenizer.from_pretrained('microsoft/DialoGPT-small'); AutoModelForCausalLM.from_pretrained('microsoft/DialoGPT-small')"

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/ || exit 1

# Run the application
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "2", "--timeout", "120", "--preload", "wsgi:app"]
```

## Step 4: Build and Push Docker Image

```bash
# Build the image
docker build -t flask-ai-wrapper .

# Tag for ECR (replace <account-id> with your AWS account ID)
docker tag flask-ai-wrapper:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/flask-ai-wrapper:latest

# Push to ECR
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/flask-ai-wrapper:latest
```

## Step 5: Create ECS Cluster

```bash
# Create ECS cluster
aws ecs create-cluster \
    --cluster-name flask-ai-cluster \
    --capacity-providers FARGATE \
    --default-capacity-provider-strategy capacityProvider=FARGATE,weight=1
```

## Step 6: Create Task Definition

Create `task-definition.json`:
```json
{
  "family": "flask-ai-wrapper",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "3072",
  "executionRoleArn": "arn:aws:iam::<account-id>:role/flask-ai-wrapper-task-role",
  "containerDefinitions": [
    {
      "name": "flask-ai-wrapper",
      "image": "<account-id>.dkr.ecr.us-east-1.amazonaws.com/flask-ai-wrapper:latest",
      "portMappings": [
        {
          "containerPort": 5000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {"name": "FLASK_ENV", "value": "production"},
        {"name": "FLASK_DEBUG", "value": "False"},
        {"name": "FLASK_HOST", "value": "0.0.0.0"},
        {"name": "FLASK_PORT", "value": "5000"},
        {"name": "AI_MODEL", "value": "microsoft/DialoGPT-small"},
        {"name": "MAX_LENGTH", "value": "512"},
        {"name": "SECRET_KEY", "value": "your-super-secret-key"},
        {"name": "CORS_ORIGINS", "value": "https://your-laravel-app.com"},
        {"name": "LOG_LEVEL", "value": "INFO"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/flask-ai-wrapper",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:5000/ || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

Register the task definition:
```bash
aws ecs register-task-definition \
    --cli-input-json file://task-definition.json
```

## Step 7: Create Application Load Balancer

### Create VPC and Subnets (if needed)
```bash
# Create VPC
aws ec2 create-vpc --cidr-block 10.0.0.0/16 --tag-specifications 'ResourceType=vpc,Tags=[{Key=Name,Value=flask-ai-vpc}]'

# Create subnets in different AZs
aws ec2 create-subnet --vpc-id <vpc-id> --cidr-block ********/24 --availability-zone us-east-1a
aws ec2 create-subnet --vpc-id <vpc-id> --cidr-block ********/24 --availability-zone us-east-1b

# Create Internet Gateway
aws ec2 create-internet-gateway
aws ec2 attach-internet-gateway --vpc-id <vpc-id> --internet-gateway-id <igw-id>
```

### Create Security Group
```bash
aws ec2 create-security-group \
    --group-name flask-ai-sg \
    --description "Security group for Flask AI wrapper" \
    --vpc-id <vpc-id>

# Allow HTTP traffic
aws ec2 authorize-security-group-ingress \
    --group-id <sg-id> \
    --protocol tcp \
    --port 80 \
    --cidr 0.0.0.0/0

# Allow HTTPS traffic
aws ec2 authorize-security-group-ingress \
    --group-id <sg-id> \
    --protocol tcp \
    --port 443 \
    --cidr 0.0.0.0/0

# Allow container port
aws ec2 authorize-security-group-ingress \
    --group-id <sg-id> \
    --protocol tcp \
    --port 5000 \
    --cidr 10.0.0.0/16
```

### Create Load Balancer
```bash
aws elbv2 create-load-balancer \
    --name flask-ai-alb \
    --subnets <subnet-id-1> <subnet-id-2> \
    --security-groups <sg-id>
```

### Create Target Group
```bash
aws elbv2 create-target-group \
    --name flask-ai-targets \
    --protocol HTTP \
    --port 5000 \
    --vpc-id <vpc-id> \
    --target-type ip \
    --health-check-path /
```

### Create Listener
```bash
aws elbv2 create-listener \
    --load-balancer-arn <alb-arn> \
    --protocol HTTP \
    --port 80 \
    --default-actions Type=forward,TargetGroupArn=<target-group-arn>
```

## Step 8: Create ECS Service

```bash
aws ecs create-service \
    --cluster flask-ai-cluster \
    --service-name flask-ai-service \
    --task-definition flask-ai-wrapper:1 \
    --desired-count 2 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[<subnet-id-1>,<subnet-id-2>],securityGroups=[<sg-id>],assignPublicIp=ENABLED}" \
    --load-balancers targetGroupArn=<target-group-arn>,containerName=flask-ai-wrapper,containerPort=5000
```

## Step 9: Set Up CloudWatch Logs

```bash
# Create log group
aws logs create-log-group --log-group-name /ecs/flask-ai-wrapper
```

## Step 10: Test Deployment

```bash
# Get load balancer DNS name
aws elbv2 describe-load-balancers --names flask-ai-alb --query 'LoadBalancers[0].DNSName' --output text

# Test health endpoint
curl http://<alb-dns-name>/

# Test chat endpoint
curl -X POST http://<alb-dns-name>/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?"}'
```

## Step 11: Set Up Domain and SSL (Optional)

### Using Route 53 and ACM
```bash
# Request SSL certificate
aws acm request-certificate \
    --domain-name your-domain.com \
    --validation-method DNS

# Create HTTPS listener
aws elbv2 create-listener \
    --load-balancer-arn <alb-arn> \
    --protocol HTTPS \
    --port 443 \
    --certificates CertificateArn=<certificate-arn> \
    --default-actions Type=forward,TargetGroupArn=<target-group-arn>
```

## Step 12: Monitoring and Scaling

### Set up Auto Scaling
```bash
aws application-autoscaling register-scalable-target \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/flask-ai-cluster/flask-ai-service \
    --min-capacity 1 \
    --max-capacity 10
```

## Estimated Costs

- **ECS Fargate**: ~$30-50/month (2 tasks, 1 vCPU, 3GB RAM each)
- **Application Load Balancer**: ~$20/month
- **Data Transfer**: Variable based on usage
- **CloudWatch Logs**: ~$5/month

## Next Steps

1. Update your Laravel app to use the ALB DNS name
2. Set up monitoring and alerts
3. Configure backup and disaster recovery
4. Implement CI/CD pipeline for updates

Your Flask AI wrapper is now running on AWS with high availability and auto-scaling!
