#!/bin/bash

# Azure deployment script for Flask Ollama Wrapper
set -e

# Configuration
RESOURCE_GROUP="flask-ollama-rg"
LOCATION="eastus"
CONTAINER_NAME="flask-ollama-wrapper"
IMAGE_NAME="flask-ollama-wrapper"
REGISTRY_NAME="your-registry"

echo "Starting Azure deployment for Flask Ollama Wrapper..."

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo "Azure CLI is not installed. Please install it first."
    exit 1
fi

# Login to Azure (if not already logged in)
echo "Checking Azure login status..."
if ! az account show &> /dev/null; then
    echo "Please login to Azure:"
    az login
fi

# Create resource group if it doesn't exist
echo "Creating resource group..."
az group create --name $RESOURCE_GROUP --location $LOCATION

# Build and push Docker image
echo "Building Docker image..."
docker build -t $IMAGE_NAME:latest .

# Tag for registry
docker tag $IMAGE_NAME:latest $REGISTRY_NAME.azurecr.io/$IMAGE_NAME:latest

# Login to Azure Container Registry
echo "Logging into Azure Container Registry..."
az acr login --name $REGISTRY_NAME

# Push image
echo "Pushing image to registry..."
docker push $REGISTRY_NAME.azurecr.io/$IMAGE_NAME:latest

# Deploy to Azure Container Instances
echo "Deploying to Azure Container Instances..."
az container create \
    --resource-group $RESOURCE_GROUP \
    --file azure-deploy.yml

# Get the public IP
echo "Getting deployment information..."
az container show \
    --resource-group $RESOURCE_GROUP \
    --name $CONTAINER_NAME \
    --query ipAddress.fqdn \
    --output tsv

echo "Deployment completed successfully!"
echo "Your Flask Ollama Wrapper is now running on Azure."
echo "Health check: curl http://$(az container show --resource-group $RESOURCE_GROUP --name $CONTAINER_NAME --query ipAddress.fqdn --output tsv)/"
