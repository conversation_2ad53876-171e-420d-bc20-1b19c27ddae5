# Flask Lightweight AI Wrapper

A lightweight Flask microservice with embedded AI models (no Ollama required), designed to be deployed on Azure and integrated with Laravel applications for route assistance.

## Features

- **No External Dependencies**: Runs lightweight AI models directly (no Ollama installation required)
- **Hybrid Intelligence**: Combines rule-based responses with AI for reliable route assistance
- **Small Footprint**: Uses compact models like DialoGPT-small (~350MB)
- **Fast Startup**: Quick initialization without external service dependencies
- **RESTful API**: Clean endpoints for Laravel integration
- **CORS Support**: Cross-origin requests enabled
- **Docker Ready**: Easy deployment with Docker
- **Azure Optimized**: Configured for Azure Container Instances
- **Route Specialized**: Optimized for driving and navigation queries

## Quick Start

### Quick Setup (Automated)

**Linux/Mac:**
```bash
chmod +x setup-lightweight.sh
./setup-lightweight.sh
```

**Windows:**
```cmd
setup-lightweight.bat
```

### Manual Setup

1. **Clone and setup**:
   ```bash
   git clone <your-repo>
   cd Flask
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your AI model preferences
   ```

5. **Run the application**:
   ```bash
   python app.py
   ```

The AI model will be automatically downloaded on first run (~350MB for DialoGPT-small).

### API Endpoints

#### Health Check
```
GET /
```
Returns service status and AI model status.

#### Chat
```
POST /chat
Content-Type: application/json

{
  "message": "What's the best route from downtown to the airport?",
  "model": "llama2"  // optional
}
```

#### List Models
```
GET /models
```
Returns information about the loaded AI model.

## Deployment

### Docker
```bash
docker build -t flask-ollama-wrapper .
docker run -p 5000:5000 --env-file .env flask-ollama-wrapper
```

### Azure Container Instances
See deployment documentation for Azure-specific instructions.

## Laravel Integration

Your Laravel app can call this service like:

```php
$response = Http::post('http://your-azure-instance:5000/chat', [
    'message' => $userMessage
]);
```

## Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run tests
pytest tests/ -v

# Run tests with coverage
pytest tests/ --cov=. --cov-report=html
```

## Laravel Integration

### 1. Install HTTP Client (if not already installed)
```bash
composer require guzzlehttp/guzzle
```

### 2. Add to your Laravel config/services.php:
```php
'ollama' => [
    'url' => env('OLLAMA_API_URL', 'http://your-azure-instance:5000'),
],
```

### 3. Create a service class:
```php
<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class OllamaService
{
    public function askRouteQuestion($userId, $message, $locationData = [])
    {
        $response = Http::timeout(30)->post(config('services.ollama.url') . '/webhook', [
            'user_id' => $userId,
            'message' => $message,
            'type' => 'route_assistance',
            'location_context' => $locationData
        ]);

        return $response->json();
    }
}
```

### 4. Use in your controller:
```php
public function askRoute(Request $request, OllamaService $ollama)
{
    $response = $ollama->askRouteQuestion(
        auth()->id(),
        $request->message,
        [
            'current_location' => $request->current_location,
            'destination' => $request->destination
        ]
    );

    return response()->json($response);
}
```

## Production Deployment

### Azure Container Instances

1. **Update configuration files**:
   - Edit `azure-deploy.yml` with your registry and settings
   - Update `.env.production` with your production values

2. **Deploy**:
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

### Manual Azure Deployment

1. **Build and push image**:
   ```bash
   docker build -t flask-ollama-wrapper .
   docker tag flask-ollama-wrapper your-registry.azurecr.io/flask-ollama-wrapper
   docker push your-registry.azurecr.io/flask-ollama-wrapper
   ```

2. **Create container instance**:
   ```bash
   az container create --resource-group your-rg --file azure-deploy.yml
   ```

## Monitoring and Logs

- Health check: `GET /`
- Detailed status: `GET /status`
- Logs are written to `./logs/` directory
- Use Azure Application Insights for production monitoring

## Environment Variables

See `.env.example` for all available configuration options.

## API Documentation

See `API_DOCUMENTATION.md` for detailed API reference.
