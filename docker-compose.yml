version: '3.8'

services:
  flask-ai-wrapper:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=True
      - AI_MODEL=microsoft/DialoGPT-small
      - MAX_LENGTH=512
    volumes:
      - ./logs:/app/logs
      - model_cache:/root/.cache/huggingface
    # Uncomment if you have GPU support
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

volumes:
  model_cache:
