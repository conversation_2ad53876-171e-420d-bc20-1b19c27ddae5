# Flask Ollama Wrapper API Documentation

## Overview

The Flask Ollama Wrapper provides a RESTful API for integrating Ollama AI models with Laravel applications. It's specifically designed for route assistance and driving queries but supports general chat functionality.

## Base URL

```
http://your-azure-instance:5000
```

## Authentication

Currently, no authentication is required. For production, consider implementing API keys or OAuth.

## Endpoints

### Health Check

**GET /**

Check the service health and Ollama connection status.

**Response:**
```json
{
  "status": "healthy",
  "service": "Flask Ollama Wrapper",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "ollama": {
    "status": "healthy",
    "host": "http://localhost:11434",
    "default_model": "llama2",
    "available_models": 2
  }
}
```

### Chat

**POST /chat**

General chat endpoint for AI conversations.

**Request Body:**
```json
{
  "message": "What's the weather like?",
  "model": "llama2",  // optional
  "context": {        // optional
    "location": "New York",
    "preferences": "avoid tolls"
  }
}
```

**Response:**
```json
{
  "response": "I don't have access to real-time weather data...",
  "model": "llama2",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "prompt_tokens": 25,
  "completion_tokens": 150,
  "total_duration": **********,
  "status": "success"
}
```

### Route Assistance

**POST /route-assistance**

Specialized endpoint for driving and route queries.

**Request Body:**
```json
{
  "message": "What's the best route from airport to downtown?",
  "current_location": "JFK Airport",
  "destination": "Times Square",
  "traffic_data": {
    "current_conditions": "heavy",
    "incidents": ["accident on I-95"]
  },
  "preferences": {
    "avoid_tolls": true,
    "fastest_route": true
  },
  "time_of_day": "rush_hour",
  "weather": "rainy"
}
```

**Response:**
```json
{
  "response": "Given the heavy traffic and rain, I recommend taking the FDR Drive instead of I-95...",
  "model": "llama2",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "status": "success",
  "request_type": "route_assistance",
  "location_context_provided": true
}
```

### Webhook

**POST /webhook**

Webhook endpoint for Laravel application integration.

**Request Body:**
```json
{
  "user_id": "user123",
  "message": "How do I get to the mall?",
  "type": "route_assistance",  // or "general"
  "request_id": "req_abc123",
  "location_context": {
    "current_location": "123 Main St",
    "destination": "Shopping Mall"
  }
}
```

**Response:**
```json
{
  "response": "To get to the mall from your location...",
  "model": "llama2",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "status": "success",
  "user_id": "user123",
  "webhook_type": "route_assistance",
  "request_id": "req_abc123"
}
```

### List Models

**GET /models**

Get available Ollama models.

**Response:**
```json
{
  "models": [
    {
      "name": "llama2",
      "size": "3.8GB",
      "modified_at": "2024-01-01T00:00:00Z",
      "digest": "sha256:abc123..."
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Status

**GET /status**

Detailed service status for monitoring.

**Response:**
```json
{
  "service": "Flask Ollama Wrapper",
  "version": "1.0.0",
  "status": "running",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "ollama": {
    "status": "healthy",
    "host": "http://localhost:11434"
  },
  "endpoints": {
    "health": "/",
    "chat": "/chat",
    "route_assistance": "/route-assistance",
    "webhook": "/webhook",
    "models": "/models",
    "status": "/status"
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error description",
  "status_code": 400,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Common Error Codes

- **400 Bad Request**: Invalid request format or missing required fields
- **503 Service Unavailable**: Ollama service is not available
- **500 Internal Server Error**: Unexpected server error

## Laravel Integration Example

```php
<?php

use Illuminate\Support\Facades\Http;

class OllamaService
{
    private $baseUrl;
    
    public function __construct()
    {
        $this->baseUrl = config('services.ollama.url');
    }
    
    public function askRouteQuestion($userId, $message, $locationContext = [])
    {
        $response = Http::post($this->baseUrl . '/webhook', [
            'user_id' => $userId,
            'message' => $message,
            'type' => 'route_assistance',
            'request_id' => uniqid('req_'),
            'location_context' => $locationContext
        ]);
        
        if ($response->successful()) {
            return $response->json();
        }
        
        throw new Exception('Failed to get AI response');
    }
}
```

## Rate Limiting

Currently no rate limiting is implemented. Consider adding rate limiting for production use.

## CORS

CORS is configured to allow requests from specified origins. Update the `CORS_ORIGINS` environment variable with your Laravel app's domain.
