#!/bin/bash

# Script to create a separate Azure VM for Ollama
set -e

# Configuration
RESOURCE_GROUP="flask-ollama-rg"
LOCATION="eastus"
OLLAMA_VM_NAME="ollama-vm"
VM_SIZE="Standard_B4ms"  # 4 vCPUs, 16GB RAM - good for Ollama
# For GPU support, use: Standard_NC6s_v3 or similar

echo "Creating Azure VM for Ollama..."

# Create VM for Ollama
az vm create \
  --resource-group $RESOURCE_GROUP \
  --name $OLLAMA_VM_NAME \
  --image Ubuntu2204 \
  --size $VM_SIZE \
  --admin-username azureuser \
  --generate-ssh-keys \
  --public-ip-sku Standard \
  --storage-sku Premium_LRS

# Open port 11434 for Ollama
az vm open-port \
  --resource-group $RESOURCE_GROUP \
  --name $OLLAMA_VM_NAME \
  --port 11434 \
  --priority 1000

# Get VM IP
VM_IP=$(az vm show \
  --resource-group $RESOURCE_GROUP \
  --name $OLLAMA_VM_NAME \
  --show-details \
  --query publicIps \
  --output tsv)

echo "VM created with IP: $VM_IP"

# Create setup script for the VM
cat > setup-ollama-on-vm.sh << 'EOF'
#!/bin/bash
set -e

echo "Installing Ollama on VM..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install curl if not present
sudo apt install -y curl

# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
sudo systemctl start ollama
sudo systemctl enable ollama

# Configure Ollama to listen on all interfaces
sudo mkdir -p /etc/systemd/system/ollama.service.d
cat > /tmp/override.conf << 'OVERRIDE'
[Service]
Environment="OLLAMA_HOST=0.0.0.0:11434"
OVERRIDE

sudo mv /tmp/override.conf /etc/systemd/system/ollama.service.d/override.conf
sudo systemctl daemon-reload
sudo systemctl restart ollama

# Wait for service to start
sleep 10

# Pull the default model
ollama pull llama2

# Optional: Pull other useful models
# ollama pull codellama
# ollama pull mistral

echo "Ollama setup complete!"
echo "Service is running on port 11434"
echo "Available models:"
ollama list
EOF

# Copy and run setup script on VM
echo "Setting up Ollama on the VM..."
scp setup-ollama-on-vm.sh azureuser@$VM_IP:~/
ssh azureuser@$VM_IP 'chmod +x setup-ollama-on-vm.sh && ./setup-ollama-on-vm.sh'

echo "Ollama VM setup complete!"
echo "VM IP: $VM_IP"
echo "Ollama URL: http://$VM_IP:11434"
echo ""
echo "Update your Flask app environment with:"
echo "OLLAMA_HOST=http://$VM_IP:11434"

# Clean up local script
rm setup-ollama-on-vm.sh
