@echo off
REM Setup script for lightweight Flask AI wrapper on Windows

echo Setting up Lightweight Flask AI Wrapper...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is required but not installed. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

REM Create virtual environment
echo Creating virtual environment...
python -m venv venv

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install requirements
echo Installing Python dependencies...
pip install -r requirements.txt

REM Create environment file if it doesn't exist
if not exist .env (
    echo Creating .env file...
    copy .env.example .env
    echo Please edit .env file with your configuration before running the app.
)

REM Create logs directory
if not exist logs mkdir logs

REM Download model (optional)
echo Pre-downloading AI model (optional, will speed up first startup)...
python -c "try: from transformers import AutoTokenizer, AutoModelForCausalLM; print('Downloading microsoft/DialoGPT-small...'); AutoTokenizer.from_pretrained('microsoft/DialoGPT-small'); AutoModelForCausalLM.from_pretrained('microsoft/DialoGPT-small'); print('Model downloaded successfully!'); except Exception as e: print(f'Model download failed (will download on first run): {e}')"

echo.
echo Setup complete! 🎉
echo.
echo To start the application:
echo 1. Edit .env file with your settings
echo 2. Run: venv\Scripts\activate.bat
echo 3. Run: python app.py
echo.
echo The app will be available at: http://localhost:5000
echo.
echo Test endpoints:
echo - Health check: curl http://localhost:5000/
echo - Chat: curl -X POST http://localhost:5000/chat -H "Content-Type: application/json" -d "{\"message\":\"Hello\"}"
echo - Route help: curl -X POST http://localhost:5000/route-assistance -H "Content-Type: application/json" -d "{\"message\":\"Best route to downtown?\"}"

pause
