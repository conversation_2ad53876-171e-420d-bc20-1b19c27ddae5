#!/bin/bash

# Setup script for lightweight Flask AI wrapper (no Ollama required)
set -e

echo "Setting up Lightweight Flask AI Wrapper..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check if pip is installed
if ! command -v pip &> /dev/null; then
    echo "pip is required but not installed. Please install pip."
    exit 1
fi

# Create virtual environment
echo "Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file..."
    cp .env.example .env
    echo "Please edit .env file with your configuration before running the app."
fi

# Create logs directory
mkdir -p logs

# Download model (optional - will be downloaded on first run if not done here)
echo "Pre-downloading AI model (optional, will speed up first startup)..."
python3 -c "
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    print('Downloading microsoft/DialoGPT-small...')
    AutoTokenizer.from_pretrained('microsoft/DialoGPT-small')
    AutoModelForCausalLM.from_pretrained('microsoft/DialoGPT-small')
    print('Model downloaded successfully!')
except Exception as e:
    print(f'Model download failed (will download on first run): {e}')
"

echo ""
echo "Setup complete! 🎉"
echo ""
echo "To start the application:"
echo "1. Edit .env file with your settings"
echo "2. Run: source venv/bin/activate"
echo "3. Run: python app.py"
echo ""
echo "The app will be available at: http://localhost:5000"
echo ""
echo "Test endpoints:"
echo "- Health check: curl http://localhost:5000/"
echo "- Chat: curl -X POST http://localhost:5000/chat -H 'Content-Type: application/json' -d '{\"message\":\"Hello\"}'"
echo "- Route help: curl -X POST http://localhost:5000/route-assistance -H 'Content-Type: application/json' -d '{\"message\":\"Best route to downtown?\"}'"
