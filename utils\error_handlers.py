"""
Error handling utilities for Flask Ollama Wrapper
"""
from flask import jsonify
import logging
from datetime import datetime
from functools import wraps

logger = logging.getLogger(__name__)

class APIError(Exception):
    """Custom API error class"""
    
    def __init__(self, message, status_code=500, payload=None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.payload = payload

class ValidationError(APIError):
    """Validation error class"""
    
    def __init__(self, message, payload=None):
        super().__init__(message, 400, payload)

class ServiceUnavailableError(APIError):
    """Service unavailable error class"""
    
    def __init__(self, message, payload=None):
        super().__init__(message, 503, payload)

def handle_api_error(error):
    """Handle API errors and return JSON response"""
    response = {
        'error': error.message,
        'status_code': error.status_code,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    if error.payload:
        response.update(error.payload)
    
    logger.error(f"API Error {error.status_code}: {error.message}")
    return jsonify(response), error.status_code

def handle_generic_error(error):
    """Handle generic exceptions"""
    logger.error(f"Unhandled error: {str(error)}", exc_info=True)
    
    response = {
        'error': 'Internal server error',
        'message': 'An unexpected error occurred',
        'timestamp': datetime.utcnow().isoformat()
    }
    
    return jsonify(response), 500

def validate_json_request(required_fields=None):
    """Decorator to validate JSON requests"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from flask import request
            
            if not request.is_json:
                raise ValidationError('Request must be JSON')
            
            data = request.get_json()
            if not data:
                raise ValidationError('Request body cannot be empty')
            
            if required_fields:
                missing_fields = []
                for field in required_fields:
                    if field not in data or not data[field]:
                        missing_fields.append(field)
                
                if missing_fields:
                    raise ValidationError(
                        f"Missing required fields: {', '.join(missing_fields)}",
                        {'missing_fields': missing_fields}
                    )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_request_response(f):
    """Decorator to log requests and responses"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from flask import request
        
        # Log request
        logger.info(f"Request: {request.method} {request.path}")
        if request.is_json:
            data = request.get_json()
            # Don't log sensitive data
            safe_data = {k: v for k, v in data.items() if k not in ['password', 'token', 'secret']}
            logger.debug(f"Request data: {safe_data}")
        
        try:
            # Execute function
            result = f(*args, **kwargs)
            
            # Log successful response
            if hasattr(result, 'status_code'):
                logger.info(f"Response: {result.status_code}")
            else:
                logger.info("Response: 200")
            
            return result
            
        except Exception as e:
            # Log error
            logger.error(f"Error in {f.__name__}: {str(e)}")
            raise
    
    return decorated_function

def rate_limit_error_handler():
    """Handle rate limiting errors"""
    response = {
        'error': 'Rate limit exceeded',
        'message': 'Too many requests. Please try again later.',
        'timestamp': datetime.utcnow().isoformat()
    }
    return jsonify(response), 429

def timeout_error_handler():
    """Handle timeout errors"""
    response = {
        'error': 'Request timeout',
        'message': 'The request took too long to process. Please try again.',
        'timestamp': datetime.utcnow().isoformat()
    }
    return jsonify(response), 408
