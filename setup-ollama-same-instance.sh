#!/bin/bash

# Setup script for installing Ollama on the same Azure VM as Flask app
set -e

echo "Setting up Ollama on Azure VM..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker if not already installed
if ! command -v docker &> /dev/null; then
    echo "Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
fi

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create directory for the application
mkdir -p ~/flask-ollama-app
cd ~/flask-ollama-app

# Create docker-compose file for both services
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    # Uncomment if you have GPU support
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  flask-app:
    build: .
    container_name: flask-ollama-wrapper
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=False
      - OLLAMA_HOST=http://ollama:11434
      - OLLAMA_MODEL=llama2
      - SECRET_KEY=${SECRET_KEY}
      - CORS_ORIGINS=${CORS_ORIGINS}
    depends_on:
      - ollama
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

volumes:
  ollama_data:
EOF

# Create environment file
cat > .env << 'EOF'
SECRET_KEY=your-production-secret-key-change-this
CORS_ORIGINS=https://your-laravel-app.com
EOF

echo "Docker Compose setup complete!"
echo "Next steps:"
echo "1. Copy your Flask app files to this directory"
echo "2. Edit .env file with your actual values"
echo "3. Run: docker-compose up -d"
echo "4. Pull Ollama model: docker exec ollama ollama pull llama2"
