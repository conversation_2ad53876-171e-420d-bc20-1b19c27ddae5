from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import logging
from dotenv import load_dotenv
from datetime import datetime
from services.ollama_service import LightweightAIService
from services.route_assistant import RouteAssistant, HybridRouteService
from utils.error_handlers import (
    APIError, ValidationError, ServiceUnavailableError,
    handle_api_error, handle_generic_error, validate_json_request, log_request_response
)
from utils.logging_config import setup_logging

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key')

# Configure CORS
CORS(app, origins=os.getenv('CORS_ORIGINS', '*').split(','))

# Setup comprehensive logging
loggers = setup_logging(app)
logger = logging.getLogger(__name__)

# Register error handlers
app.register_error_handler(APIError, handle_api_error)
app.register_error_handler(ValidationError, handle_api_error)
app.register_error_handler(ServiceUnavailableError, handle_api_error)
app.register_error_handler(Exception, handle_generic_error)

# AI Model configuration
AI_MODEL = os.getenv('AI_MODEL', 'microsoft/DialoGPT-small')
MAX_LENGTH = int(os.getenv('MAX_LENGTH', '512'))

# Initialize lightweight AI service
ai_service = LightweightAIService(model_name=AI_MODEL, max_length=MAX_LENGTH)

# Initialize route assistant and hybrid service
route_assistant = RouteAssistant()
hybrid_service = HybridRouteService(ai_service, route_assistant)

@app.route('/', methods=['GET'])
@log_request_response
def health_check():
    """Health check endpoint"""
    ai_health = ai_service.health_check()

    return jsonify({
        'status': 'healthy',
        'service': 'Flask Ollama Wrapper',
        'timestamp': datetime.utcnow().isoformat(),
        'ai_model': ai_health
    })

@app.route('/chat', methods=['POST'])
@log_request_response
@validate_json_request(['message'])
def chat():
    """Main chat endpoint for processing user messages"""
    data = request.get_json()
    message = data.get('message', '').strip()

    # Check if AI service is available
    if not ai_service.is_available():
        raise ServiceUnavailableError('AI service unavailable')

    # Get model and context from request
    model = data.get('model')
    location_context = data.get('context', {})

    logger.info(f"Processing chat request with model: {model or 'default'}")

    # Use specialized route assistance if this looks like a route query
    route_keywords = ['route', 'direction', 'way', 'drive', 'traffic', 'road', 'navigate']
    is_route_query = any(keyword in message.lower() for keyword in route_keywords)

    if is_route_query:
        response_data = hybrid_service.generate_route_response(message, location_context)
    else:
        response_data = ai_service.generate_response(message, model, context=location_context)

    logger.info("Chat request processed successfully")
    return jsonify(response_data)

@app.route('/models', methods=['GET'])
def list_models():
    """List available Ollama models"""
    try:
        if not ai_service.is_available():
            return jsonify({'error': 'AI service unavailable'}), 503

        models = ai_service.list_models()
        return jsonify({
            'models': models,
            'timestamp': datetime.utcnow().isoformat()
        })

    except Exception as e:
        logger.error(f"Error listing models: {e}")
        return jsonify({
            'error': 'Failed to list models',
            'message': str(e)
        }), 500

@app.route('/route-assistance', methods=['POST'])
def route_assistance():
    """Specialized endpoint for route and driving assistance"""
    try:
        if not request.is_json:
            return jsonify({'error': 'Request must be JSON'}), 400

        data = request.get_json()
        message = data.get('message', '').strip()

        if not message:
            return jsonify({'error': 'Message is required'}), 400

        if not ai_service.is_available():
            return jsonify({'error': 'AI service unavailable'}), 503

        # Extract location context from Laravel app
        location_context = {
            'current_location': data.get('current_location'),
            'destination': data.get('destination'),
            'traffic_data': data.get('traffic_data'),
            'preferences': data.get('preferences', {}),
            'time_of_day': data.get('time_of_day'),
            'weather': data.get('weather')
        }

        logger.info("Processing route assistance request")

        response_data = hybrid_service.generate_route_response(message, location_context)

        # Add additional metadata for Laravel app
        response_data.update({
            'request_type': 'route_assistance',
            'location_context_provided': bool(location_context.get('current_location'))
        })

        return jsonify(response_data)

    except ValueError as e:
        logger.warning(f"Validation error in route assistance: {e}")
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error in route assistance: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@app.route('/webhook', methods=['POST'])
def webhook():
    """Webhook endpoint for Laravel app integration"""
    try:
        if not request.is_json:
            return jsonify({'error': 'Request must be JSON'}), 400

        data = request.get_json()

        # Validate webhook payload
        required_fields = ['user_id', 'message']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        user_id = data.get('user_id')
        message = data.get('message', '').strip()
        webhook_type = data.get('type', 'general')

        if not message:
            return jsonify({'error': 'Message cannot be empty'}), 400

        if not ai_service.is_available():
            return jsonify({'error': 'AI service unavailable'}), 503

        logger.info(f"Processing webhook request for user {user_id}, type: {webhook_type}")

        # Route to appropriate handler based on webhook type
        if webhook_type == 'route_assistance':
            location_context = data.get('location_context', {})
            response_data = hybrid_service.generate_route_response(message, location_context)
        else:
            # General chat
            context = data.get('context', {})
            model = data.get('model')
            response_data = ai_service.generate_response(message, model, context=context)

        # Add webhook-specific metadata
        response_data.update({
            'user_id': user_id,
            'webhook_type': webhook_type,
            'request_id': data.get('request_id')
        })

        logger.info(f"Webhook request processed successfully for user {user_id}")
        return jsonify(response_data)

    except ValueError as e:
        logger.warning(f"Validation error in webhook: {e}")
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@app.route('/status', methods=['GET'])
def status():
    """Detailed status endpoint for monitoring"""
    try:
        ai_health = ai_service.health_check()

        return jsonify({
            'service': 'Flask Ollama Wrapper',
            'version': '1.0.0',
            'status': 'running',
            'timestamp': datetime.utcnow().isoformat(),
            'ai_model': ai_health,
            'endpoints': {
                'health': '/',
                'chat': '/chat',
                'route_assistance': '/route-assistance',
                'webhook': '/webhook',
                'models': '/models',
                'status': '/status'
            }
        })

    except Exception as e:
        logger.error(f"Error in status endpoint: {e}")
        return jsonify({
            'service': 'Flask Ollama Wrapper',
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

if __name__ == '__main__':
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting Flask app on {host}:{port}")
    app.run(host=host, port=port, debug=debug)
