#!/bin/bash

# AWS Cleanup Script for Flask AI Wrapper
# WARNING: This will delete all AWS resources created for the Flask AI Wrapper
set -e

# Configuration
AWS_REGION="us-east-1"
CLUSTER_NAME="flask-ai-cluster"
SERVICE_NAME="flask-ai-service"
REPOSITORY_NAME="flask-ai-wrapper"
TASK_FAMILY="flask-ai-wrapper"

echo "⚠️  WARNING: This will delete all AWS resources for Flask AI Wrapper"
echo "This includes:"
echo "- ECS Service and Cluster"
echo "- ECR Repository and Images"
echo "- CloudWatch Log Groups"
echo "- Security Groups"
echo "- IAM Roles"
echo ""
read -p "Are you sure you want to continue? (yes/no): " -r
if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo "Cleanup cancelled."
    exit 0
fi

echo "Starting cleanup..."

# Stop and delete ECS service
echo "Stopping ECS service..."
aws ecs update-service \
    --cluster $CLUSTER_NAME \
    --service $SERVICE_NAME \
    --desired-count 0 \
    --region $AWS_REGION 2>/dev/null || echo "Service not found or already stopped"

echo "Waiting for service to stop..."
aws ecs wait services-stable \
    --cluster $CLUSTER_NAME \
    --services $SERVICE_NAME \
    --region $AWS_REGION 2>/dev/null || echo "Service wait skipped"

echo "Deleting ECS service..."
aws ecs delete-service \
    --cluster $CLUSTER_NAME \
    --service $SERVICE_NAME \
    --region $AWS_REGION 2>/dev/null || echo "Service not found"

# Delete ECS cluster
echo "Deleting ECS cluster..."
aws ecs delete-cluster \
    --cluster $CLUSTER_NAME \
    --region $AWS_REGION 2>/dev/null || echo "Cluster not found"

# Delete task definitions
echo "Deregistering task definitions..."
TASK_DEFINITION_ARNS=$(aws ecs list-task-definitions \
    --family-prefix $TASK_FAMILY \
    --query 'taskDefinitionArns' \
    --output text \
    --region $AWS_REGION 2>/dev/null || echo "")

for arn in $TASK_DEFINITION_ARNS; do
    if [ ! -z "$arn" ]; then
        echo "Deregistering $arn"
        aws ecs deregister-task-definition \
            --task-definition $arn \
            --region $AWS_REGION 2>/dev/null || echo "Failed to deregister $arn"
    fi
done

# Delete ECR repository
echo "Deleting ECR repository..."
aws ecr delete-repository \
    --repository-name $REPOSITORY_NAME \
    --force \
    --region $AWS_REGION 2>/dev/null || echo "Repository not found"

# Delete CloudWatch log group
echo "Deleting CloudWatch log group..."
aws logs delete-log-group \
    --log-group-name "/ecs/$TASK_FAMILY" \
    --region $AWS_REGION 2>/dev/null || echo "Log group not found"

# Delete security groups
echo "Deleting security groups..."
SG_IDS=$(aws ec2 describe-security-groups \
    --filters "Name=group-name,Values=flask-ai-sg*" \
    --query 'SecurityGroups[].GroupId' \
    --output text \
    --region $AWS_REGION 2>/dev/null || echo "")

for sg_id in $SG_IDS; do
    if [ ! -z "$sg_id" ]; then
        echo "Deleting security group $sg_id"
        aws ec2 delete-security-group \
            --group-id $sg_id \
            --region $AWS_REGION 2>/dev/null || echo "Failed to delete security group $sg_id"
    fi
done

# Delete IAM role
echo "Deleting IAM role..."
aws iam detach-role-policy \
    --role-name flask-ai-wrapper-task-role \
    --policy-arn arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy 2>/dev/null || echo "Policy not attached"

aws iam delete-role \
    --role-name flask-ai-wrapper-task-role 2>/dev/null || echo "Role not found"

echo ""
echo "✅ Cleanup completed!"
echo ""
echo "All AWS resources for Flask AI Wrapper have been deleted."
echo "Note: This script only deletes resources created by the deployment script."
echo "If you created additional resources manually, you may need to delete them separately."
