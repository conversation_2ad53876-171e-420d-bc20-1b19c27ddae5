"""
Logging configuration for Flask Ollama Wrapper
"""
import logging
import logging.handlers
import os
from datetime import datetime

def setup_logging(app):
    """Setup comprehensive logging for the application"""
    
    # Get log level from environment
    log_level = getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper())
    
    # Create logs directory if it doesn't exist
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),  # Console output
            logging.handlers.RotatingFileHandler(
                os.path.join(log_dir, 'app.log'),
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
        ]
    )
    
    # Configure Flask app logger
    app.logger.setLevel(log_level)
    
    # Create separate loggers for different components
    
    # API requests logger
    api_logger = logging.getLogger('api')
    api_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'api.log'),
        maxBytes=10*1024*1024,
        backupCount=5
    )
    api_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))
    api_logger.addHandler(api_handler)
    api_logger.setLevel(log_level)
    
    # Ollama service logger
    ollama_logger = logging.getLogger('ollama')
    ollama_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'ollama.log'),
        maxBytes=10*1024*1024,
        backupCount=5
    )
    ollama_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))
    ollama_logger.addHandler(ollama_handler)
    ollama_logger.setLevel(log_level)
    
    # Error logger
    error_logger = logging.getLogger('errors')
    error_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'errors.log'),
        maxBytes=10*1024*1024,
        backupCount=10
    )
    error_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s - %(pathname)s:%(lineno)d'
    ))
    error_logger.addHandler(error_handler)
    error_logger.setLevel(logging.ERROR)
    
    # Performance logger
    perf_logger = logging.getLogger('performance')
    perf_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'performance.log'),
        maxBytes=5*1024*1024,
        backupCount=3
    )
    perf_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(message)s'
    ))
    perf_logger.addHandler(perf_handler)
    perf_logger.setLevel(logging.INFO)
    
    app.logger.info("Logging configuration completed")
    
    return {
        'api': api_logger,
        'ollama': ollama_logger,
        'errors': error_logger,
        'performance': perf_logger
    }

def log_performance(operation, duration, details=None):
    """Log performance metrics"""
    perf_logger = logging.getLogger('performance')
    
    message = f"Operation: {operation}, Duration: {duration:.3f}s"
    if details:
        message += f", Details: {details}"
    
    perf_logger.info(message)

def log_api_request(method, path, user_id=None, response_time=None, status_code=None):
    """Log API request details"""
    api_logger = logging.getLogger('api')
    
    message = f"{method} {path}"
    if user_id:
        message += f" - User: {user_id}"
    if response_time:
        message += f" - Time: {response_time:.3f}s"
    if status_code:
        message += f" - Status: {status_code}"
    
    api_logger.info(message)

def log_ollama_interaction(model, prompt_length, response_length, duration):
    """Log Ollama interaction details"""
    ollama_logger = logging.getLogger('ollama')
    
    message = (f"Model: {model}, Prompt: {prompt_length} chars, "
              f"Response: {response_length} chars, Duration: {duration:.3f}s")
    
    ollama_logger.info(message)
