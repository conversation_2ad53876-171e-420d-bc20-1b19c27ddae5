"""
Specialized route assistance service with predefined responses and logic
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import re

logger = logging.getLogger(__name__)

class RouteAssistant:
    """Specialized route assistance with rule-based responses"""
    
    def __init__(self):
        """Initialize route assistant with predefined responses"""
        self.route_patterns = {
            'traffic': [
                'traffic', 'congestion', 'jam', 'busy', 'crowded', 'slow'
            ],
            'fastest': [
                'fastest', 'quickest', 'quick', 'fast', 'speed', 'time'
            ],
            'avoid': [
                'avoid', 'skip', 'bypass', 'alternative', 'different'
            ],
            'directions': [
                'direction', 'way', 'route', 'path', 'how to get', 'navigate'
            ],
            'distance': [
                'distance', 'far', 'miles', 'km', 'kilometers', 'close'
            ],
            'tolls': [
                'toll', 'fee', 'cost', 'expensive', 'cheap'
            ]
        }
        
        self.responses = {
            'traffic': [
                "Based on current traffic conditions, I recommend checking real-time traffic apps like Google Maps or Waze for the most up-to-date route information.",
                "Traffic can vary throughout the day. Consider leaving during off-peak hours if possible.",
                "Highway routes may have heavy traffic during rush hours. Local roads might be a better alternative."
            ],
            'fastest': [
                "The fastest route typically involves major highways, but this can change based on traffic conditions.",
                "For the quickest route, I recommend using GPS navigation with real-time traffic data.",
                "Highway routes are usually fastest, but consider traffic patterns for your specific time of travel."
            ],
            'avoid': [
                "To avoid traffic, consider taking alternate routes through local roads.",
                "You can avoid tolls by selecting 'avoid tolls' in your navigation app.",
                "Construction zones and accident-prone areas should be avoided when possible."
            ],
            'directions': [
                "For detailed turn-by-turn directions, I recommend using Google Maps, Apple Maps, or Waze.",
                "Make sure to enter your exact starting point and destination for accurate directions.",
                "Consider checking multiple route options before starting your journey."
            ],
            'distance': [
                "Distance can vary significantly depending on the route taken.",
                "The most direct route isn't always the shortest in terms of travel time.",
                "Use mapping applications to get accurate distance measurements."
            ],
            'tolls': [
                "Most navigation apps allow you to avoid toll roads if preferred.",
                "Toll roads often provide faster travel times but at an additional cost.",
                "Consider the time savings versus cost when deciding on toll routes."
            ],
            'general': [
                "For the best route assistance, please provide your starting location and destination.",
                "I recommend using real-time navigation apps for the most current route information.",
                "Consider factors like traffic, weather, and road conditions when planning your route.",
                "Always check for road closures or construction before starting your journey."
            ]
        }
    
    def analyze_query(self, message: str) -> List[str]:
        """Analyze the user's message to identify route-related topics"""
        message_lower = message.lower()
        identified_topics = []
        
        for topic, keywords in self.route_patterns.items():
            if any(keyword in message_lower for keyword in keywords):
                identified_topics.append(topic)
        
        return identified_topics if identified_topics else ['general']
    
    def generate_response(self, message: str, context: Optional[Dict] = None) -> str:
        """Generate a route assistance response"""
        topics = self.analyze_query(message)
        
        # Build response based on identified topics
        response_parts = []
        
        for topic in topics[:2]:  # Limit to 2 topics to keep response concise
            if topic in self.responses:
                response_parts.append(self.responses[topic][0])
        
        if not response_parts:
            response_parts.append(self.responses['general'][0])
        
        # Add context-specific information if available
        if context:
            if context.get('current_location') and context.get('destination'):
                response_parts.append(
                    f"For your trip from {context['current_location']} to {context['destination']}, "
                    "I recommend using a real-time navigation app for the most accurate route."
                )
            elif context.get('traffic_data'):
                response_parts.append(
                    "Based on current traffic conditions, consider alternative routes if available."
                )
        
        return " ".join(response_parts)
    
    def get_route_suggestions(self, start: str, destination: str) -> Dict[str, Any]:
        """Provide general route suggestions"""
        return {
            'suggestions': [
                f"Use Google Maps or Waze for real-time directions from {start} to {destination}",
                "Check traffic conditions before departing",
                "Consider alternative routes during peak hours",
                "Allow extra time for unexpected delays"
            ],
            'tips': [
                "Enable voice navigation for hands-free driving",
                "Check for road closures or construction",
                "Consider fuel stops for longer journeys",
                "Share your route with family or friends for safety"
            ]
        }

class HybridRouteService:
    """Combines AI model with rule-based route assistance"""
    
    def __init__(self, ai_service, route_assistant):
        """Initialize hybrid service"""
        self.ai_service = ai_service
        self.route_assistant = route_assistant
        
    def generate_route_response(self, message: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Generate response using both AI and rule-based approaches"""
        try:
            # Get rule-based response (fast and reliable)
            rule_response = self.route_assistant.generate_response(message, context)
            
            # Try to enhance with AI if available
            if self.ai_service.is_available():
                try:
                    # Create a focused prompt for route assistance
                    enhanced_prompt = f"Route question: {message}. Provide helpful driving advice."
                    ai_result = self.ai_service.generate_response(
                        enhanced_prompt, 
                        context=context
                    )
                    
                    # Combine responses intelligently
                    if len(ai_result.get('response', '')) > 20:
                        combined_response = f"{rule_response}\n\nAdditional advice: {ai_result['response']}"
                    else:
                        combined_response = rule_response
                        
                    return {
                        'response': combined_response,
                        'model': f"hybrid-{ai_result.get('model', 'unknown')}",
                        'timestamp': datetime.utcnow().isoformat(),
                        'method': 'hybrid',
                        'status': 'success'
                    }
                    
                except Exception as e:
                    logger.warning(f"AI enhancement failed, using rule-based response: {e}")
            
            # Fallback to rule-based response
            return {
                'response': rule_response,
                'model': 'rule-based',
                'timestamp': datetime.utcnow().isoformat(),
                'method': 'rule-based',
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"Error in hybrid route service: {e}")
            return {
                'response': "I'm having trouble processing your route question. Please try using a navigation app like Google Maps for the most accurate directions.",
                'model': 'fallback',
                'timestamp': datetime.utcnow().isoformat(),
                'method': 'fallback',
                'status': 'error',
                'error': str(e)
            }
