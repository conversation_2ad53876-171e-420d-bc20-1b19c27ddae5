"""
Test cases for Flask Ollama Wrapper
"""
import pytest
import json
from unittest.mock import Mock, patch
import sys
import os

# Add the parent directory to the path so we can import our app
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app

@pytest.fixture
def client():
    """Create a test client"""
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

@pytest.fixture
def mock_ollama_service():
    """Mock the Ollama service"""
    with patch('app.ollama_service') as mock:
        mock.is_available.return_value = True
        mock.health_check.return_value = {
            'status': 'healthy',
            'host': 'http://localhost:11434',
            'default_model': 'llama2'
        }
        mock.generate_response.return_value = {
            'response': 'Test response',
            'model': 'llama2',
            'timestamp': '2024-01-01T00:00:00',
            'status': 'success'
        }
        mock.generate_route_assistance.return_value = {
            'response': 'Take the highway for fastest route',
            'model': 'llama2',
            'timestamp': '2024-01-01T00:00:00',
            'status': 'success'
        }
        mock.list_models.return_value = [
            {'name': 'llama2', 'size': '3.8GB'},
            {'name': 'codellama', 'size': '3.8GB'}
        ]
        yield mock

class TestHealthCheck:
    """Test health check endpoint"""
    
    def test_health_check_success(self, client, mock_ollama_service):
        """Test successful health check"""
        response = client.get('/')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert data['service'] == 'Flask Ollama Wrapper'
        assert 'timestamp' in data
        assert 'ollama' in data

class TestChatEndpoint:
    """Test chat endpoint"""
    
    def test_chat_success(self, client, mock_ollama_service):
        """Test successful chat request"""
        payload = {'message': 'Hello, how are you?'}
        
        response = client.post('/chat', 
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'response' in data
        assert data['status'] == 'success'
    
    def test_chat_missing_message(self, client, mock_ollama_service):
        """Test chat request with missing message"""
        payload = {}
        
        response = client.post('/chat',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_chat_empty_message(self, client, mock_ollama_service):
        """Test chat request with empty message"""
        payload = {'message': ''}
        
        response = client.post('/chat',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 400
    
    def test_chat_non_json(self, client, mock_ollama_service):
        """Test chat request with non-JSON data"""
        response = client.post('/chat', data='not json')
        assert response.status_code == 400
    
    def test_chat_service_unavailable(self, client):
        """Test chat when Ollama service is unavailable"""
        with patch('app.ollama_service') as mock:
            mock.is_available.return_value = False
            
            payload = {'message': 'Hello'}
            response = client.post('/chat',
                                 data=json.dumps(payload),
                                 content_type='application/json')
            
            assert response.status_code == 503

class TestRouteAssistance:
    """Test route assistance endpoint"""
    
    def test_route_assistance_success(self, client, mock_ollama_service):
        """Test successful route assistance request"""
        payload = {
            'message': 'What is the best route to downtown?',
            'current_location': 'Airport',
            'destination': 'Downtown'
        }
        
        response = client.post('/route-assistance',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'response' in data
        assert data['request_type'] == 'route_assistance'

class TestWebhook:
    """Test webhook endpoint"""
    
    def test_webhook_success(self, client, mock_ollama_service):
        """Test successful webhook request"""
        payload = {
            'user_id': 'user123',
            'message': 'Hello from Laravel',
            'type': 'general',
            'request_id': 'req123'
        }
        
        response = client.post('/webhook',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['user_id'] == 'user123'
        assert data['webhook_type'] == 'general'
    
    def test_webhook_missing_user_id(self, client, mock_ollama_service):
        """Test webhook request with missing user_id"""
        payload = {'message': 'Hello'}
        
        response = client.post('/webhook',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 400

class TestModels:
    """Test models endpoint"""
    
    def test_list_models_success(self, client, mock_ollama_service):
        """Test successful models listing"""
        response = client.get('/models')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'models' in data
        assert len(data['models']) > 0

class TestStatus:
    """Test status endpoint"""
    
    def test_status_success(self, client, mock_ollama_service):
        """Test successful status request"""
        response = client.get('/status')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['service'] == 'Flask Ollama Wrapper'
        assert data['status'] == 'running'
        assert 'endpoints' in data

if __name__ == '__main__':
    pytest.main([__file__])
