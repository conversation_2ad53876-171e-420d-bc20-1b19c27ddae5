# Deployment Guide for Flask Ollama Wrapper

## Prerequisites

1. **Azure Account**: Active Azure subscription
2. **Azure CLI**: Installed and configured
3. **Docker**: Installed locally
4. **Ollama Instance**: Running Ollama server (can be on same or different Azure instance)

## Step-by-Step Deployment

### 1. Prepare Your Environment

```bash
# Clone your repository
git clone <your-repo-url>
cd Flask

# Copy environment file
cp .env.example .env.production

# Edit production environment variables
nano .env.production
```

### 2. Set Up Azure Resources

```bash
# Login to Azure
az login

# Create resource group
az group create --name flask-ollama-rg --location eastus

# Create Azure Container Registry (optional, for private images)
az acr create --resource-group flask-ollama-rg --name youracr --sku Basic
```

### 3. Configure Ollama Instance

#### Option A: Deploy Ollama on Azure VM

```bash
# Create VM for Ollama
az vm create \
  --resource-group flask-ollama-rg \
  --name ollama-vm \
  --image Ubuntu2204 \
  --size Standard_B2s \
  --admin-username azureuser \
  --generate-ssh-keys

# SSH into VM and install Ollama
ssh azureuser@<vm-ip>
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
sudo systemctl start ollama
sudo systemctl enable ollama

# Pull your desired model
ollama pull llama2
```

#### Option B: Use Existing Ollama Instance

Update your `.env.production` with the Ollama host URL:
```
OLLAMA_HOST=http://your-ollama-instance:11434
```

### 4. Build and Deploy Flask App

#### Using the Deployment Script

```bash
# Make script executable
chmod +x deploy.sh

# Edit script with your settings
nano deploy.sh

# Run deployment
./deploy.sh
```

#### Manual Deployment

```bash
# Build Docker image
docker build -t flask-ollama-wrapper .

# Tag for Azure Container Registry (if using)
docker tag flask-ollama-wrapper youracr.azurecr.io/flask-ollama-wrapper

# Login to ACR
az acr login --name youracr

# Push image
docker push youracr.azurecr.io/flask-ollama-wrapper

# Deploy to Azure Container Instances
az container create \
  --resource-group flask-ollama-rg \
  --name flask-ollama-wrapper \
  --image youracr.azurecr.io/flask-ollama-wrapper \
  --cpu 1 \
  --memory 2 \
  --ports 5000 \
  --environment-variables \
    FLASK_ENV=production \
    FLASK_DEBUG=False \
    OLLAMA_HOST=http://your-ollama-vm-ip:11434 \
    SECRET_KEY=your-secret-key \
    CORS_ORIGINS=https://your-laravel-app.com
```

### 5. Configure Networking

```bash
# Get the public IP of your Flask app
az container show \
  --resource-group flask-ollama-rg \
  --name flask-ollama-wrapper \
  --query ipAddress.fqdn \
  --output tsv

# If using VM for Ollama, open port 11434
az vm open-port \
  --resource-group flask-ollama-rg \
  --name ollama-vm \
  --port 11434
```

### 6. Test Deployment

```bash
# Health check
curl http://your-flask-app-fqdn:5000/

# Test chat endpoint
curl -X POST http://your-flask-app-fqdn:5000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?"}'
```

## Laravel App Configuration

### 1. Update Laravel Environment

Add to your Laravel `.env`:
```
OLLAMA_API_URL=http://your-flask-app-fqdn:5000
```

### 2. Create Laravel Service

```php
<?php
// app/Services/OllamaService.php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OllamaService
{
    private $baseUrl;
    
    public function __construct()
    {
        $this->baseUrl = config('services.ollama.url');
    }
    
    public function askRouteQuestion($userId, $message, $locationContext = [])
    {
        try {
            $response = Http::timeout(30)->post($this->baseUrl . '/webhook', [
                'user_id' => $userId,
                'message' => $message,
                'type' => 'route_assistance',
                'request_id' => uniqid('req_'),
                'location_context' => $locationContext
            ]);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('Ollama API error', ['response' => $response->body()]);
            throw new \Exception('AI service unavailable');
            
        } catch (\Exception $e) {
            Log::error('Ollama service error', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
```

### 3. Create Controller

```php
<?php
// app/Http/Controllers/RouteAssistantController.php

namespace App\Http\Controllers;

use App\Services\OllamaService;
use Illuminate\Http\Request;

class RouteAssistantController extends Controller
{
    public function ask(Request $request, OllamaService $ollama)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'current_location' => 'nullable|string',
            'destination' => 'nullable|string'
        ]);
        
        try {
            $response = $ollama->askRouteQuestion(
                auth()->id(),
                $request->message,
                [
                    'current_location' => $request->current_location,
                    'destination' => $request->destination,
                    'preferences' => $request->preferences ?? []
                ]
            );
            
            return response()->json([
                'success' => true,
                'data' => $response
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to get route assistance at this time'
            ], 500);
        }
    }
}
```

## Monitoring and Maintenance

### 1. Set Up Monitoring

```bash
# Create Application Insights
az monitor app-insights component create \
  --app flask-ollama-insights \
  --location eastus \
  --resource-group flask-ollama-rg

# Get instrumentation key
az monitor app-insights component show \
  --app flask-ollama-insights \
  --resource-group flask-ollama-rg \
  --query instrumentationKey
```

### 2. Log Monitoring

```bash
# View container logs
az container logs \
  --resource-group flask-ollama-rg \
  --name flask-ollama-wrapper

# Stream logs
az container logs \
  --resource-group flask-ollama-rg \
  --name flask-ollama-wrapper \
  --follow
```

### 3. Health Monitoring

Set up automated health checks in your monitoring system:
- Endpoint: `http://your-flask-app:5000/`
- Expected response: `{"status": "healthy"}`
- Check interval: 1 minute

## Scaling and Performance

### 1. Horizontal Scaling

For higher traffic, consider:
- Azure Container Apps for auto-scaling
- Azure Kubernetes Service (AKS)
- Load balancer with multiple instances

### 2. Performance Optimization

- Use faster Azure VM sizes for Ollama (GPU-enabled for better performance)
- Implement caching for frequent queries
- Use Azure CDN for static content

## Security Considerations

1. **API Security**: Implement API keys or OAuth
2. **Network Security**: Use Azure Virtual Networks
3. **SSL/TLS**: Enable HTTPS with Azure Application Gateway
4. **Secrets Management**: Use Azure Key Vault for sensitive data

## Troubleshooting

### Common Issues

1. **Ollama Connection Failed**:
   - Check Ollama service status
   - Verify network connectivity
   - Check firewall rules

2. **Container Won't Start**:
   - Check environment variables
   - Review container logs
   - Verify image build

3. **High Response Times**:
   - Monitor Ollama performance
   - Check Azure VM resources
   - Consider model optimization

### Debug Commands

```bash
# Check container status
az container show --resource-group flask-ollama-rg --name flask-ollama-wrapper

# Restart container
az container restart --resource-group flask-ollama-rg --name flask-ollama-wrapper

# Update container
az container create --resource-group flask-ollama-rg --file azure-deploy.yml
```
