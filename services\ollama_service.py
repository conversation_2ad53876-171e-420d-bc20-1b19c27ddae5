"""
Lightweight AI service module for handling direct model interactions
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import torch
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    pipeline,
    TextGenerationPipeline
)
import os

logger = logging.getLogger(__name__)

class LightweightAIService:
    """Service class for direct lightweight model interactions"""

    def __init__(self, model_name: str = "microsoft/DialoGPT-small", max_length: int = 512):
        """
        Initialize lightweight AI service

        Args:
            model_name: HuggingFace model name to use
            max_length: Maximum response length
        """
        self.model_name = model_name
        self.max_length = max_length
        self.tokenizer = None
        self.model = None
        self.pipeline = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self._initialize_model()
    
    def _initialize_model(self) -> None:
        """Initialize the lightweight model with error handling"""
        try:
            logger.info(f"Loading model: {self.model_name} on device: {self.device}")

            # Load tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None
            )

            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # Create text generation pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )

            logger.info(f"Model {self.model_name} loaded successfully on {self.device}")

        except Exception as e:
            logger.error(f"Failed to initialize model: {e}")
            self.tokenizer = None
            self.model = None
            self.pipeline = None
    
    def is_available(self) -> bool:
        """Check if AI service is available"""
        return self.pipeline is not None
    
    def list_models(self) -> List[Dict[str, Any]]:
        """
        Get list of available models (currently loaded model)

        Returns:
            List of model information dictionaries
        """
        if not self.is_available():
            raise Exception("AI service is not available")

        try:
            # Return info about the currently loaded model
            model_info = {
                'name': self.model_name,
                'type': 'lightweight',
                'device': self.device,
                'max_length': self.max_length,
                'status': 'loaded'
            }

            logger.info(f"Current model: {self.model_name}")
            return [model_info]

        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            raise
    
    def generate_response(self,
                         message: str,
                         model: Optional[str] = None,
                         system_prompt: Optional[str] = None,
                         context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Generate AI response for a given message

        Args:
            message: User message
            model: Model to use (ignored, uses loaded model)
            system_prompt: System prompt to guide the AI
            context: Additional context information

        Returns:
            Dictionary containing response and metadata
        """
        if not self.is_available():
            raise Exception("AI service is not available")

        if not message.strip():
            raise ValueError("Message cannot be empty")

        try:
            start_time = datetime.utcnow()

            # Build the prompt
            prompt = self._build_prompt(message, system_prompt, context)

            logger.info(f"Generating response with model: {self.model_name}")

            # Generate response using the pipeline
            response = self.pipeline(
                prompt,
                max_length=min(len(prompt.split()) + 150, self.max_length),
                num_return_sequences=1,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                truncation=True
            )

            # Extract the generated text (remove the input prompt)
            generated_text = response[0]['generated_text']
            ai_response = generated_text[len(prompt):].strip()

            # If response is empty or too short, provide a fallback
            if len(ai_response) < 10:
                ai_response = "I understand your question about routes. Could you provide more specific details about your starting point and destination?"

            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            result = {
                'response': ai_response,
                'model': self.model_name,
                'timestamp': end_time.isoformat(),
                'prompt_length': len(prompt),
                'response_length': len(ai_response),
                'generation_time': duration,
                'status': 'success'
            }

            logger.info(f"Response generated successfully in {duration:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            raise
    
    def _build_prompt(self,
                     message: str,
                     system_prompt: Optional[str] = None,
                     context: Optional[Dict] = None) -> str:
        """
        Build the complete prompt for the AI model

        Args:
            message: User message
            system_prompt: System instructions
            context: Additional context

        Returns:
            Complete prompt string
        """
        prompt_parts = []

        # Add system prompt for route assistance
        if system_prompt:
            prompt_parts.append(system_prompt)
        else:
            # Default system prompt for route assistance
            prompt_parts.append("You are a helpful driving assistant. Provide clear, concise route advice.")

        # Add context if provided
        if context:
            if context.get('current_location'):
                prompt_parts.append(f"Starting from: {context['current_location']}")
            if context.get('destination'):
                prompt_parts.append(f"Going to: {context['destination']}")
            if context.get('preferences'):
                prompt_parts.append(f"Preferences: {context['preferences']}")

        # Add user message
        prompt_parts.append(f"Question: {message}")
        prompt_parts.append("Answer:")

        return " ".join(prompt_parts)
    
    def generate_route_assistance(self,
                                 message: str,
                                 location_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Specialized method for route assistance queries

        Args:
            message: User's route-related question
            location_context: Location information from Google Maps API

        Returns:
            AI response optimized for route assistance
        """
        system_prompt = """You are a driving assistant. Provide helpful route advice. Consider traffic, road conditions, and time of day. Keep responses clear and practical."""

        return self.generate_response(
            message=message,
            system_prompt=system_prompt,
            context=location_context
        )
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on AI service

        Returns:
            Health status information
        """
        try:
            if not self.is_available():
                return {
                    'status': 'unhealthy',
                    'message': 'AI model not loaded',
                    'timestamp': datetime.utcnow().isoformat()
                }

            # Try a simple generation as a health test
            test_response = self.pipeline(
                "Hello",
                max_length=20,
                num_return_sequences=1,
                do_sample=False
            )

            return {
                'status': 'healthy',
                'model': self.model_name,
                'device': self.device,
                'max_length': self.max_length,
                'test_generation': 'successful',
                'timestamp': datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'unhealthy',
                'message': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
